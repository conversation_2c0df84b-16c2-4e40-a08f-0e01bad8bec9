using System;
using DG.Tweening;
using UnityEngine;

public class CreatureMovement : MonoBehaviour
{
    private CreatureMovementData _data;
    private Transform _visualsTransform;

    private float _currentSpeed;
    private float _movementTime;
    private float _stationaryTime;
    private bool _isMoving;
    private bool _wasMoving;
    private bool _isReducingSpeed;
    private Vector3 _lastPosition;
    private float _distanceToTarget;

    [SerializeField]
    private float StoppingDistance = 0.25f;

    public CreatureMovementData Data;
    public Transform TargetPosition;
    private Sequence _bounceSequence;

    private void Awake()
    {
        _visualsTransform = transform.GetChild(0);
        _lastPosition = transform.position;
    }

    private void Start()
    {
        Initialize(Data);
    }

    public void Initialize(CreatureMovementData data)
    {
        _data = data;
    }

    public void Update()
    {
        UpdateMovementState();
        MoveTowardsTarget();
        AnimateMovement();
    }

    private void UpdateMovementState()
    {
        // Check if creature has moved significantly
        float distanceMoved = Vector3.Distance(transform.position, _lastPosition);
        _wasMoving = _isMoving;
        _isMoving = distanceMoved > 0.01f; // Small threshold to account for floating point precision

        // Update movement and stationary timers
        if (_isMoving)
        {
            _movementTime += Time.deltaTime;
            _stationaryTime = 0f;
        }
        else
        {
            _stationaryTime += Time.deltaTime;
            if (_stationaryTime > _data.StationaryResetTime)
            {
                _movementTime = 0f; // Reset movement time after being stationary
            }
        }

        _lastPosition = transform.position;

        // Calculate distance to target for approach slowing
        if (TargetPosition != null)
        {
            _distanceToTarget = Vector3.Distance(transform.position, TargetPosition.position);
        }
    }

    private void MoveTowardsTarget()
    {
        if (TargetPosition == null)
        {
            ReduceSpeed();
            return;
        }

        if (_distanceToTarget <= StoppingDistance)
        {
            ReduceSpeed();
            return;
        }

        var direction = TargetPosition.position - transform.position;
        direction.y = 0;
        direction.Normalize();

        Move(direction);
    }

    private void AnimateMovement()
    {
        if (_bounceSequence != null && _bounceSequence.IsPlaying())
        {
            return;
        }
            
        var percentMaxSpeed = (_currentSpeed / _data.MoveSpeed);

        var jumpPower = _isReducingSpeed ? Mathf.Clamp01(_data.StepHeight * (percentMaxSpeed - 0.05f)) : _data.StepHeight;
        _bounceSequence = _visualsTransform.DOLocalJump(Vector3.zero, jumpPower, 1, _data.StepInterval)
            .SetEase(Ease.OutCubic);
    }

    private void ReduceSpeed()
    {
        if (Mathf.Approximately(_currentSpeed, 0))
        {
            _currentSpeed = 0;
            _isReducingSpeed = false;
            return;
        }

        _isReducingSpeed = true;

        // Enhanced deceleration based on movement time
        float decelerationRate = CalculateDecelerationRate();
        _currentSpeed = Mathf.Lerp(_currentSpeed, 0, decelerationRate * Time.deltaTime);
    }

    private void Move(Vector3 direction)
    {
        if (direction == Vector3.zero)
            return;

        // Calculate target speed with time-based acceleration and approach slowing
        float targetSpeed = CalculateTargetSpeed();

        // Enhanced acceleration based on movement time
        float accelerationRate = CalculateAccelerationRate();
        _currentSpeed = Mathf.Lerp(_currentSpeed, targetSpeed, accelerationRate * Time.deltaTime);

        var distanceToTravel = direction * (_currentSpeed * Time.deltaTime);
        transform.position += distanceToTravel;
    }

    private float CalculateTargetSpeed()
    {
        float baseSpeed = _data.MoveSpeed;

        // Time-based speed multiplier (creature gets faster the longer it moves)
        float timeMultiplier = 1f;
        if (_movementTime > _data.AccelerationBuildupTime)
        {
            float excessTime = _movementTime - _data.AccelerationBuildupTime;
            timeMultiplier = 1f + (excessTime / _data.MaxAccelerationTime) * _data.MaxSpeedMultiplier;
            timeMultiplier = Mathf.Clamp(timeMultiplier, 1f, 1f + _data.MaxSpeedMultiplier);
        }

        // Distance-based approach slowing
        float approachMultiplier = 1f;
        if (_distanceToTarget <= _data.ApproachSlowDistance)
        {
            // Use a smooth curve for approach slowing
            float normalizedDistance = _distanceToTarget / _data.ApproachSlowDistance;
            approachMultiplier = Mathf.Lerp(_data.MinApproachSpeed, 1f, normalizedDistance);
        }

        return baseSpeed * timeMultiplier * approachMultiplier;
    }

    private float CalculateAccelerationRate()
    {
        // Faster acceleration when starting from stationary
        if (_movementTime < _data.InitialAccelerationTime)
        {
            return _data.Acceleration * _data.InitialAccelerationMultiplier;
        }

        return _data.Acceleration;
    }

    private float CalculateDecelerationRate()
    {
        // Faster deceleration if creature was moving for a long time (momentum loss)
        if (_movementTime > _data.AccelerationBuildupTime)
        {
            return _data.Deceleration * _data.MomentumDecelerationMultiplier;
        }

        return _data.Deceleration;
    }
}