using UnityEngine;
using UnityEngine.InputSystem;

/// <summary>
/// <PERSON>les clicking on focusable objects to set them as the camera's focus point
/// </summary>
public class ClickToFocus : MonoBehaviour
{
    [Header("Raycast Settings")]
    [SerializeField] private LayerMask focusableLayerMask = -1;
    [SerializeField] private float maxRaycastDistance = 100f;
    
    private Camera mainCamera;
    private CameraController _cameraController;
    private PlayerControls _playerControls;
    private IFocusable currentFocusedObject;
    
    private PlayerControls PlayerControls
    {
        get
        {
            if (_playerControls == null)
            {
                _playerControls = ServiceLocator.Locate<PlayerControls>();
            }
            
            if (_playerControls == null)
            {
                _playerControls = new PlayerControls();
            }
            
            return _playerControls;
        }
    }
    
    private CameraController CameraController
    {
        get
        {
            if (_cameraController == null)
            {
                _cameraController = ServiceLocator.Locate<CameraController>();
            }
            
            if (_cameraController == null)
            {
                _cameraController = new CameraController();
            }
            
            return _cameraController;
        }
    }
    
    private void Awake()
    {
        mainCamera = Camera.main;
        
        ServiceLocator.Register(this);
    }
    
    private void Start()
    {
        PlayerControls.Camera.Click.performed += OnClick;
    }
    
    private void OnDestroy()
    {
        if (_playerControls != null)
        {
            PlayerControls.Camera.Click.performed -= OnClick;
        }
    }
    
    private void OnEnable()
    {
        PlayerControls.Camera.Enable();
    }
    
    private void OnDisable()
    {
        PlayerControls.Camera.Disable();
    }
    
    private void OnClick(InputAction.CallbackContext context)
    {
        if (!context.performed) return;
        
        // Get mouse position
        Vector2 mousePosition = PlayerControls.Camera.Point.ReadValue<Vector2>();
        
        // Perform raycast from camera through mouse position
        Ray ray = mainCamera.ScreenPointToRay(mousePosition);
        
        if (Physics.Raycast(ray, out RaycastHit hit, maxRaycastDistance, focusableLayerMask))
        {
            // Check if the hit object has an IFocusable component
            IFocusable focusable = hit.collider.GetComponent<IFocusable>();
            
            if (focusable != null && focusable.CanBeFocused)
            {
                SetFocusTarget(focusable);
            }
            else
            {
                // Check parent objects for IFocusable
                focusable = hit.collider.GetComponentInParent<IFocusable>();
                if (focusable != null && focusable.CanBeFocused)
                {
                    SetFocusTarget(focusable);
                }
            }
        }
    }
    
    private void SetFocusTarget(IFocusable focusable)
    {
        if (focusable == currentFocusedObject) return;
        
        // Notify previous focused object
        if (currentFocusedObject != null)
        {
            currentFocusedObject.OnFocusLost();
        }
        
        currentFocusedObject = focusable;
        currentFocusedObject.OnFocused();
        
        CameraController.SetFocusPoint(focusable);
    }
    
    private void ClearFocus()
    {
        if (currentFocusedObject != null)
        {
            currentFocusedObject.OnFocusLost();
            currentFocusedObject = null;
        }
        
        CameraController.ClearFocusPoint();
    }
    
    /// <summary>
    /// Get the currently focused object
    /// </summary>
    public IFocusable GetCurrentFocusedObject()
    {
        return currentFocusedObject;
    }
    
    /// <summary>
    /// Manually clear the current focus
    /// </summary>
    public void ClearCurrentFocus()
    {
        ClearFocus();
    }
}
