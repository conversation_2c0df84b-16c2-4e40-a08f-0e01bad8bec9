using UnityEngine;

public class CameraControllerTEMP : MonoBeh<PERSON>our
{
    [SerializeField]
    private Transform FocusPoint;
    
    private CameraController _cameraController;
    private CameraController CameraController
    {
        get
        {
            if (_cameraController == null)
            {
                _cameraController = ServiceLocator.Locate<CameraController>();
            }
            
            return _cameraController;
        }
    }
    
    [ContextMenu("Set Focus Point")]
    public void SetFocusPoint()
    {
        if (!FocusPoint)
        {
            return;
        }
        
        CameraController.SetFocusPoint(FocusPoint);
    }
    
    [ContextMenu("Clear Focus Point")]
    public void ClearFocusPoint()
    {
        CameraController.ClearFocusPoint();
    }
}
