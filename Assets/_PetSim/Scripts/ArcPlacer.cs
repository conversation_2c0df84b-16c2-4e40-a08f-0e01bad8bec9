using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Places child GameObjects evenly spaced along a 180-degree arc above the MonoBehaviour
/// </summary>
public class ArcPlacer : MonoBehaviour
{
    [Header("Arc Settings")]
    [SerializeField] private float radius = 5f;
    [SerializeField] private float arcAngle = 180f;
    [SerializeField] private float heightOffset = 0f;
    [SerializeField] private bool faceCenter = true;
    
    [Header("Child Object Settings")]
    [SerializeField] private GameObject childPrefab;
    [SerializeField] private int numberOfObjects = 5;
    [SerializeField] private bool includeEndpoints = true;
    
    [Header("Auto Update")]
    [SerializeField] private bool autoUpdateInEditor = true;
    [SerializeField] private bool clearExistingChildren = true;
    
    [Header("Debug")]
    [SerializeField] private bool showGizmos = true;
    [SerializeField] private Color gizmoColor = Color.yellow;
    
    private List<GameObject> placedObjects = new List<GameObject>();
    
    private void Start()
    {
        if (Application.isPlaying && childPrefab != null)
        {
            PlaceObjects();
        }
    }
    
    /// <summary>
    /// Place objects along the arc
    /// </summary>
    [ContextMenu("Place Objects")]
    public void PlaceObjects()
    {
        if (childPrefab == null)
        {
            Debug.LogWarning("ArcPlacer: No child prefab assigned!");
            return;
        }
        
        if (numberOfObjects <= 0)
        {
            Debug.LogWarning("ArcPlacer: Number of objects must be greater than 0!");
            return;
        }
        
        // Clear existing children if requested
        if (clearExistingChildren)
        {
            ClearPlacedObjects();
        }
        
        // Calculate positions and place objects
        Vector3[] positions = CalculateArcPositions();
        
        for (int i = 0; i < positions.Length; i++)
        {
            GameObject newObject = CreateChildObject(positions[i], i);
            if (newObject != null)
            {
                placedObjects.Add(newObject);
            }
        }
        
        Debug.Log($"ArcPlacer: Placed {placedObjects.Count} objects along arc");
    }
    
    /// <summary>
    /// Calculate positions along the arc
    /// </summary>
    private Vector3[] CalculateArcPositions()
    {
        Vector3[] positions = new Vector3[numberOfObjects];
        
        // Convert arc angle to radians
        float arcRadians = arcAngle * Mathf.Deg2Rad;
        
        // Calculate angle step between objects
        float angleStep;
        if (includeEndpoints && numberOfObjects > 1)
        {
            angleStep = arcRadians / (numberOfObjects - 1);
        }
        else
        {
            angleStep = arcRadians / numberOfObjects;
        }
        
        // Starting angle (negative half of arc to center it)
        float startAngle = -arcRadians * 0.5f;
        
        for (int i = 0; i < numberOfObjects; i++)
        {
            float currentAngle;
            
            if (includeEndpoints && numberOfObjects > 1)
            {
                currentAngle = startAngle + (angleStep * i);
            }
            else
            {
                currentAngle = startAngle + (angleStep * i) + (angleStep * 0.5f);
            }
            
            // Calculate position on the arc
            float x = Mathf.Sin(currentAngle) * radius;
            float z = Mathf.Cos(currentAngle) * radius;
            float y = heightOffset;
            
            // Convert to world position
            Vector3 localPosition = new Vector3(x, y, z);
            positions[i] = transform.TransformPoint(localPosition);
        }
        
        return positions;
    }
    
    /// <summary>
    /// Create a child object at the specified position
    /// </summary>
    private GameObject CreateChildObject(Vector3 position, int index)
    {
        GameObject newObject;
        
        if (Application.isPlaying)
        {
            newObject = Instantiate(childPrefab, position, Quaternion.identity, transform);
        }
        else
        {
#if UNITY_EDITOR
            newObject = UnityEditor.PrefabUtility.InstantiatePrefab(childPrefab, transform) as GameObject;
            if (newObject != null)
            {
                newObject.transform.position = position;
            }
#else
            newObject = Instantiate(childPrefab, position, Quaternion.identity, transform);
#endif
        }
        
        if (newObject != null)
        {
            // Set name
            newObject.name = $"{childPrefab.name}_Arc_{index:00}";
            
            // Make object face center if requested
            if (faceCenter)
            {
                Vector3 directionToCenter = transform.position - newObject.transform.position;
                directionToCenter.y = 0; // Keep rotation only on Y axis
                
                if (directionToCenter != Vector3.zero)
                {
                    newObject.transform.rotation = Quaternion.LookRotation(directionToCenter);
                }
            }
        }
        
        return newObject;
    }
    
    /// <summary>
    /// Clear all placed objects
    /// </summary>
    [ContextMenu("Clear Objects")]
    public void ClearPlacedObjects()
    {
        // Clear tracked objects
        for (int i = placedObjects.Count - 1; i >= 0; i--)
        {
            if (placedObjects[i] != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(placedObjects[i]);
                }
                else
                {
                    DestroyImmediate(placedObjects[i]);
                }
            }
        }
        placedObjects.Clear();
        
        // Also clear any children that might not be tracked
        if (clearExistingChildren)
        {
            for (int i = transform.childCount - 1; i >= 0; i--)
            {
                Transform child = transform.GetChild(i);
                if (Application.isPlaying)
                {
                    Destroy(child.gameObject);
                }
                else
                {
                    DestroyImmediate(child.gameObject);
                }
            }
        }
    }
    
    /// <summary>
    /// Update positions of existing objects
    /// </summary>
    [ContextMenu("Update Positions")]
    public void UpdatePositions()
    {
        if (placedObjects.Count == 0) return;
        
        Vector3[] positions = CalculateArcPositions();
        
        for (int i = 0; i < Mathf.Min(placedObjects.Count, positions.Length); i++)
        {
            if (placedObjects[i] != null)
            {
                placedObjects[i].transform.position = positions[i];
                
                // Update rotation if facing center
                if (faceCenter)
                {
                    Vector3 directionToCenter = transform.position - placedObjects[i].transform.position;
                    directionToCenter.y = 0;
                    
                    if (directionToCenter != Vector3.zero)
                    {
                        placedObjects[i].transform.rotation = Quaternion.LookRotation(directionToCenter);
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Get all placed objects
    /// </summary>
    public List<GameObject> GetPlacedObjects()
    {
        return new List<GameObject>(placedObjects);
    }
    
    /// <summary>
    /// Set the number of objects and update
    /// </summary>
    public void SetNumberOfObjects(int count)
    {
        numberOfObjects = Mathf.Max(1, count);
        if (autoUpdateInEditor || Application.isPlaying)
        {
            PlaceObjects();
        }
    }
    
    /// <summary>
    /// Set the radius and update
    /// </summary>
    public void SetRadius(float newRadius)
    {
        radius = Mathf.Max(0.1f, newRadius);
        if (autoUpdateInEditor || Application.isPlaying)
        {
            UpdatePositions();
        }
    }
    
    private void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        Gizmos.color = gizmoColor;
        
        // Draw arc
        Vector3[] positions = CalculateArcPositions();
        
        // Draw arc line
        for (int i = 0; i < positions.Length - 1; i++)
        {
            Gizmos.DrawLine(positions[i], positions[i + 1]);
        }
        
        // Draw position markers
        foreach (Vector3 pos in positions)
        {
            Gizmos.DrawWireSphere(pos, 0.2f);
        }
        
        // Draw center point
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, 0.3f);
    }
    
#if UNITY_EDITOR
    private void OnValidate()
    {
        if (autoUpdateInEditor && !Application.isPlaying)
        {
            // Delay the update to avoid issues during validation
            UnityEditor.EditorApplication.delayCall += () =>
            {
                if (this != null)
                {
                    UpdatePositions();
                }
            };
        }
    }
#endif
}
