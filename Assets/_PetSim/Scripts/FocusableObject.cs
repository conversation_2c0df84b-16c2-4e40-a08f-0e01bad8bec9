using UnityEngine;

/// <summary>
/// Basic implementation of IFocusable for any GameObject that can be focused by the camera
/// </summary>
public class FocusableObject : MonoBehaviour, IFocusable
{
    [Header("Focus Settings")]
    [SerializeField] private Transform focusTransform;
    [SerializeField] private bool canBeFocused = true;
    
    [Header("Visual Feedback")]
    [SerializeField] private GameObject focusIndicator;
    [SerializeField] private Material focusedMaterial;
    [SerializeField] private Material normalMaterial;
    
    private Renderer objectRenderer;
    private bool isFocused = false;
    
    public Transform FocusTransform 
    { 
        get 
        { 
            // Use specified focus transform, or fall back to this object's transform
            return focusTransform != null ? focusTransform : transform; 
        } 
    }
    
    public bool CanBeFocused 
    { 
        get { return canBeFocused && gameObject.activeInHierarchy; } 
    }
    
    private void Awake()
    {
        // Get renderer for visual feedback
        objectRenderer = GetComponent<Renderer>();
        
        // If no focus transform specified, use this object's transform
        if (focusTransform == null)
        {
            focusTransform = transform;
        }
        
        // Hide focus indicator initially
        if (focusIndicator != null)
        {
            focusIndicator.SetActive(false);
        }
    }
    
    public void OnFocused()
    {
        isFocused = true;
        
        // Show visual feedback
        if (focusIndicator != null)
        {
            focusIndicator.SetActive(true);
        }
        
        // Change material if available
        if (objectRenderer != null && focusedMaterial != null)
        {
            objectRenderer.material = focusedMaterial;
        }
        
        // Optional: Add any custom focus behavior here
        OnFocusedCustom();
    }
    
    public void OnFocusLost()
    {
        isFocused = false;
        
        // Hide visual feedback
        if (focusIndicator != null)
        {
            focusIndicator.SetActive(false);
        }
        
        // Restore normal material
        if (objectRenderer != null && normalMaterial != null)
        {
            objectRenderer.material = normalMaterial;
        }
        
        // Optional: Add any custom unfocus behavior here
        OnFocusLostCustom();
    }
    
    /// <summary>
    /// Override this method to add custom behavior when focused
    /// </summary>
    protected virtual void OnFocusedCustom()
    {
        // Override in derived classes for custom focus behavior
    }
    
    /// <summary>
    /// Override this method to add custom behavior when focus is lost
    /// </summary>
    protected virtual void OnFocusLostCustom()
    {
        // Override in derived classes for custom unfocus behavior
    }
    
    /// <summary>
    /// Enable or disable the ability to be focused
    /// </summary>
    public void SetCanBeFocused(bool canFocus)
    {
        canBeFocused = canFocus;
    }
    
    /// <summary>
    /// Check if this object is currently focused
    /// </summary>
    public bool IsFocused()
    {
        return isFocused;
    }
    
    /// <summary>
    /// Set a custom focus transform
    /// </summary>
    public void SetFocusTransform(Transform newFocusTransform)
    {
        focusTransform = newFocusTransform;
    }
}
