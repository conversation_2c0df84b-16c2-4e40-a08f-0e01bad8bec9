using UnityEngine;

[CreateAssetMenu(fileName = "CreatureMovementData", menuName = "Data/CreatureMovementData")]
public class CreatureMovementData : ScriptableObject
{
    [Header("Basic Movement")]
    public float MoveSpeed = 1f;
    public float Acceleration = 2f;
    public float Deceleration = 2f;

    [Head<PERSON>("Time-Based Acceleration")]
    [Tooltip("Time before acceleration buildup starts")]
    public float AccelerationBuildupTime = 1f;
    [Tooltip("Time to reach maximum speed multiplier")]
    public float MaxAccelerationTime = 3f;
    [Tooltip("Maximum speed multiplier when fully accelerated")]
    public float MaxSpeedMultiplier = 0.5f;
    [Tooltip("Time for enhanced initial acceleration")]
    public float InitialAccelerationTime = 0.5f;
    [Tooltip("Acceleration multiplier for initial movement")]
    public float InitialAccelerationMultiplier = 2f;
    [Tooltip("Time stationary before resetting movement time")]
    public float StationaryResetTime = 1f;
    [<PERSON>lt<PERSON>("Deceleration multiplier when losing momentum")]
    public float MomentumDecelerationMultiplier = 1.5f;

    [Head<PERSON>("Approach Slowing")]
    [Tooltip("Distance from target to start slowing down")]
    public float ApproachSlowDistance = 2f;
    [Tooltip("Minimum speed when approaching target (0-1)")]
    [Range(0.1f, 1f)]
    public float MinApproachSpeed = 0.3f;

    [Header("Animation")]
    public float StepSize = 0.05f;
    public float StepInterval = 0.05f;
    public float StepHeight = 0.01f;
}