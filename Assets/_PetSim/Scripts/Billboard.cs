using UnityEngine;

/// <summary>
/// Makes the GameObject always face the main camera
/// Useful for UI elements, health bars, name tags, etc.
/// </summary>
public class Billboard : MonoBehaviour
{
    [Header("Billboard Settings")]
    [SerializeField] private BillboardType billboardType = BillboardType.LookAtCamera;
    [SerializeField] private bool lockX = false;
    [SerializeField] private bool lockY = false;
    [SerializeField] private bool lockZ = false;
    
    [<PERSON><PERSON>("Performance")]
    [SerializeField] private bool useFixedUpdate = false;
    [SerializeField] private bool useLateUpdate = true;
    
    [Header("Camera Reference")]
    [SerializeField] private Camera targetCamera;
    
    private Transform cameraTransform;
    private Vector3 originalRotation;
    
    public enum BillboardType
    {
        LookAtCamera,           // Face the camera directly
        LookAtCameraInverted,   // Face away from the camera
        CameraForward,          // Align with camera's forward direction
        CameraForwardInverted   // Align opposite to camera's forward direction
    }
    
    private void Start()
    {
        // Get camera reference
        if (targetCamera == null)
        {
            targetCamera = Camera.main;
            if (targetCamera == null)
            {
                targetCamera = FindObjectOfType<Camera>();
            }
        }
        
        if (targetCamera != null)
        {
            cameraTransform = targetCamera.transform;
        }
        else
        {
            Debug.LogWarning("Billboard: No camera found! Billboard effect will not work.");
        }
        
        // Store original rotation for reference
        originalRotation = transform.eulerAngles;
    }
    
    private void Update()
    {
        if (!useFixedUpdate && !useLateUpdate)
        {
            UpdateBillboard();
        }
    }
    
    private void FixedUpdate()
    {
        if (useFixedUpdate)
        {
            UpdateBillboard();
        }
    }
    
    private void LateUpdate()
    {
        if (useLateUpdate)
        {
            UpdateBillboard();
        }
    }
    
    private void UpdateBillboard()
    {
        if (cameraTransform == null) return;
        
        Vector3 targetDirection = Vector3.zero;
        
        switch (billboardType)
        {
            case BillboardType.LookAtCamera:
                targetDirection = cameraTransform.position - transform.position;
                break;
                
            case BillboardType.LookAtCameraInverted:
                targetDirection = transform.position - cameraTransform.position;
                break;
                
            case BillboardType.CameraForward:
                targetDirection = cameraTransform.forward;
                break;
                
            case BillboardType.CameraForwardInverted:
                targetDirection = -cameraTransform.forward;
                break;
        }
        
        // Apply axis locks
        if (lockX) targetDirection.x = 0;
        if (lockY) targetDirection.y = 0;
        if (lockZ) targetDirection.z = 0;
        
        // Only rotate if we have a valid direction
        if (targetDirection != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(targetDirection);
            
            // Apply the rotation
            transform.rotation = targetRotation;
        }
    }
    
    /// <summary>
    /// Set a custom camera to billboard towards
    /// </summary>
    public void SetTargetCamera(Camera camera)
    {
        targetCamera = camera;
        cameraTransform = camera != null ? camera.transform : null;
    }
    
    /// <summary>
    /// Change the billboard type at runtime
    /// </summary>
    public void SetBillboardType(BillboardType type)
    {
        billboardType = type;
    }
    
    /// <summary>
    /// Lock or unlock specific axes
    /// </summary>
    public void SetAxisLocks(bool x, bool y, bool z)
    {
        lockX = x;
        lockY = y;
        lockZ = z;
    }
    
    /// <summary>
    /// Reset to original rotation
    /// </summary>
    public void ResetToOriginalRotation()
    {
        transform.eulerAngles = originalRotation;
    }
}
