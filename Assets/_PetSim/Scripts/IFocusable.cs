using UnityEngine;

/// <summary>
/// Interface for objects that can be focused by the camera system
/// </summary>
public interface IFocusable
{
    /// <summary>
    /// The transform that the camera should focus on
    /// </summary>
    Transform FocusTransform { get; }
    
    /// <summary>
    /// Called when this object becomes the camera's focus target
    /// </summary>
    void OnFocused();
    
    /// <summary>
    /// Called when this object loses camera focus
    /// </summary>
    void OnFocusLost();
    
    /// <summary>
    /// Whether this object can currently be focused
    /// </summary>
    bool CanBeFocused { get; }
}
