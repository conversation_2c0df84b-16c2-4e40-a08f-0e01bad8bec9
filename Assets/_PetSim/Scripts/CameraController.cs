using DG.Tweening;
using UnityEngine;
using UnityEngine.InputSystem;

public class CameraController : MonoBehaviour
{
    private const float MOVE_TO_FOCUS_DURATION = 0.6f;

    [Header("Focus Point Settings")]
    [SerializeField] private Transform focusPoint;
    [SerializeField] private float focusLerpSpeed = 2f;
    [SerializeField] private Vector3 focusOffset = new Vector3(-2.5f, 1, -2.5f);

    [Header("Pan Settings")]
    [SerializeField] private float panSpeed = 0.2f;
    [SerializeField] private float sprintSpeed = 0.4f;
    [SerializeField] private float panBoundary = 5f;

    [Header("Zoom Settings")]
    [SerializeField] private float zoomSpeed = 2f;
    [SerializeField] private float minZoom = 3f;
    [SerializeField] private float maxZoom = 15f;

    // Private fields
    private Camera cam;
    private ClickToFocus _clickToFocus;
    private Vector3 targetPosition;
    private Vector3 lastMousePosition;
    private bool isSprintEnabled;
    private bool isPanning;
    private bool isFollowingFocus;
    private Tween focusTween;

    private Vector2 _currentAltMovementInput;
    
    private PlayerControls _playerControls;

    private PlayerControls PlayerControls
    {
        get
        {
            if (_playerControls == null)
            {
                _playerControls = ServiceLocator.Locate<PlayerControls>();
            }
            
            if (_playerControls == null)
            {
                _playerControls = new PlayerControls();
            }
            
            return _playerControls;
        }
    }
    
    private void Awake()
    {
        ServiceLocator.Register(this);
        cam = GetComponent<Camera>();
        _clickToFocus = GetComponent<ClickToFocus>();

        // Set initial target position
        targetPosition = transform.position;
        isFollowingFocus = focusPoint != null;
    }

    private void Start()
    {
        PlayerControls.Camera.Move.performed += OnMove;
        PlayerControls.Camera.AltMovement.performed += OnAltMovementChanged;
        PlayerControls.Camera.AltMovement.canceled += OnAltMovementChanged;
        PlayerControls.Camera.Sprint.started += OnSprintStarted;
        PlayerControls.Camera.Sprint.canceled += OnSprintCancelled;
    }

    private void OnDestroy()
    {
        PlayerControls.Camera.Move.performed -= OnMove;
        PlayerControls.Camera.AltMovement.started -= OnAltMovementChanged;
        PlayerControls.Camera.AltMovement.canceled -= OnAltMovementChanged;
        PlayerControls.Camera.Sprint.started -= OnSprintStarted;
        PlayerControls.Camera.Sprint.canceled -= OnSprintCancelled;
    }

    private void Update()
    {
        if(_currentAltMovementInput != Vector2.zero)
            AdjustTargetPosition(_currentAltMovementInput);
    }

    private void FixedUpdate()
    {
        if (isFollowingFocus)
        {
            // Smoothly move towards focus point
            transform.position = Vector3.Lerp(transform.position, focusPoint.position + focusOffset, focusLerpSpeed * Time.fixedDeltaTime);
        }
        else
        {
            // Move towards target position
            transform.position = Vector3.Lerp(transform.position, targetPosition, focusLerpSpeed * Time.fixedDeltaTime);
        }
    }

    private void OnMove(InputAction.CallbackContext ctx)
    {
        var movementInput = ctx.ReadValue<Vector2>();
        
        if (movementInput == Vector2.zero)
            return;
        
        _clickToFocus.ClearCurrentFocus();
        
        AdjustTargetPosition(movementInput);
    }

    private void OnAltMovementChanged(InputAction.CallbackContext ctx)
    {
        var movementInput = ctx.ReadValue<Vector2>();
        
        _clickToFocus.ClearCurrentFocus();
        
        _currentAltMovementInput = movementInput;
    }

    private void OnSprintStarted(InputAction.CallbackContext ctx)
    {
        isSprintEnabled = true;
    }

    private void OnSprintCancelled(InputAction.CallbackContext ctx)
    {
        isSprintEnabled = false;
    }

    private void OnEnable()
    {
        PlayerControls.Camera.Enable();
    }
    
    private void OnDisable()
    {
        PlayerControls.Camera.Disable();
    }

    private void AdjustTargetPosition(Vector2 delta)
    {
        var speed = isSprintEnabled ? sprintSpeed : panSpeed;

        // add movementInput to targetPosition relative to camera's forward and right
        targetPosition += transform.right * (delta.x * speed);
        
        // transform forward projected on the global xy plane
        var forward = transform.forward;
        forward.y = 0;
        forward.Normalize();
        
        targetPosition += forward * (delta.y * speed);
    }

    #region Public API

    /// <summary>
    /// Sets a new focus point for the camera to track
    /// </summary>
    /// <param name="newFocusPoint">The transform to focus on</param>
    public void SetFocusPoint(Transform newFocusPoint)
    {
        if (newFocusPoint == focusPoint)
            return;
        
        if (newFocusPoint == null)
        {
            targetPosition = focusPoint.position + focusOffset;
        }
        
        focusPoint = newFocusPoint;

        if (focusPoint != null)
        {
            // Use DOTween for smooth transition to new focus point
            Vector3 targetPos = focusPoint.position + focusOffset;
            focusTween?.Kill();
            focusTween = DOVirtual.Vector3(targetPosition, targetPos, MOVE_TO_FOCUS_DURATION, x => targetPosition = x)
                .SetEase(Ease.InOutSine)
                .OnComplete(() => isFollowingFocus = true);
        }
        else
        {
            isFollowingFocus = false;
        }
    }

    /// <summary>
    /// Manually moves the camera to a specific position with smooth transition
    /// </summary>
    public void MoveTo(Vector3 position, float duration = 1f)
    {
        isFollowingFocus = false;
        focusTween?.Kill();
        focusTween = transform.DOMove(position, duration).SetEase(Ease.OutCubic);
    }

    #endregion
}
