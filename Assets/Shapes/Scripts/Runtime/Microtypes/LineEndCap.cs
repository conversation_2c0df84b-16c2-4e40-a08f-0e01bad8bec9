// Shapes © <PERSON><PERSON> - https://twitter.com/Freya<PERSON>olmer/
// Website & Documentation - https://acegikmo.com/shapes/

namespace Shapes {

	/// <summary>Types of line end caps</summary>
	public enum LineEndCap {
		/// <summary>No end caps</summary>
		None,

		/// <summary>Square end caps. Looks the same as none, but extends further than the endpoints</summary>
		Square,

		/// <summary>Rounded end caps</summary>
		Round
	}

}