using UnityEngine;

// Shapes © Freya Holmér - https://twitter.com/FreyaHolmer/
// Website & Documentation - https://acegikmo.com/shapes/
// this file is auto-generated by CodegenInterfaces
namespace Shapes {

	public partial class Disc {
		[SerializeField] bool matchDashSpacingToSize = true;
		public bool MatchDashSpacingToSize {
			get => matchDashSpacingToSize;
			set {
				matchDashSpacingToSize = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] bool dashed = false;
		public bool Dashed {
			get => dashed;
			set {
				dashed = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] DashStyle dashStyle = DashStyle.defaultDashStyleRing;
		public float DashSize {
			get => dashStyle.size;
			set {
				dashStyle.size = value;
				float netDashSize = dashStyle.GetNetAbsoluteSize( dashed, thickness );
				if( matchDashSpacingToSize )
					SetFloat( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
				SetFloatNow( ShapesMaterialUtils.propDashSize, netDashSize );
			}
		}
		public float DashSpacing {
			get => matchDashSpacingToSize ? dashStyle.size : dashStyle.spacing;
			set {
				dashStyle.spacing = value;
				SetFloatNow( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
			}
		}
		public float DashOffset {
			get => dashStyle.offset;
			set => SetFloatNow( ShapesMaterialUtils.propDashOffset, dashStyle.offset = value );
		}
		public DashSpace DashSpace {
			get => dashStyle.space;
			set {
				SetInt( ShapesMaterialUtils.propDashSpace, (int)( dashStyle.space = value ) );
				SetFloatNow( ShapesMaterialUtils.propDashSize, dashStyle.GetNetAbsoluteSize( dashed, thickness ) );
			}
		}
		public DashSnapping DashSnap {
			get => dashStyle.snap;
			set => SetIntNow( ShapesMaterialUtils.propDashSnap, (int)( dashStyle.snap = value ) );
		}
		public DashType DashType {
			get => dashStyle.type;
			set => SetIntNow( ShapesMaterialUtils.propDashType, (int)( dashStyle.type = value ) );
		}
		public float DashShapeModifier {
			get => dashStyle.shapeModifier;
			set => SetFloatNow( ShapesMaterialUtils.propDashShapeModifier, dashStyle.shapeModifier = value );
		}
		void SetAllDashValues( bool now ) => SetAllDashValues( dashStyle, Dashed, matchDashSpacingToSize, thickness, setType: true, now );
		float GetNetDashSpacing() => GetNetDashSpacing( dashStyle, dashed, matchDashSpacingToSize, thickness );
	}

	public partial class Line {
		[SerializeField] bool matchDashSpacingToSize = true;
		public bool MatchDashSpacingToSize {
			get => matchDashSpacingToSize;
			set {
				matchDashSpacingToSize = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] bool dashed = false;
		public bool Dashed {
			get => dashed;
			set {
				dashed = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] DashStyle dashStyle = DashStyle.defaultDashStyleLine;
		public float DashSize {
			get => dashStyle.size;
			set {
				dashStyle.size = value;
				float netDashSize = dashStyle.GetNetAbsoluteSize( dashed, thickness );
				if( matchDashSpacingToSize )
					SetFloat( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
				SetFloatNow( ShapesMaterialUtils.propDashSize, netDashSize );
			}
		}
		public float DashSpacing {
			get => matchDashSpacingToSize ? dashStyle.size : dashStyle.spacing;
			set {
				dashStyle.spacing = value;
				SetFloatNow( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
			}
		}
		public float DashOffset {
			get => dashStyle.offset;
			set => SetFloatNow( ShapesMaterialUtils.propDashOffset, dashStyle.offset = value );
		}
		public DashSpace DashSpace {
			get => dashStyle.space;
			set {
				SetInt( ShapesMaterialUtils.propDashSpace, (int)( dashStyle.space = value ) );
				SetFloatNow( ShapesMaterialUtils.propDashSize, dashStyle.GetNetAbsoluteSize( dashed, thickness ) );
			}
		}
		public DashSnapping DashSnap {
			get => dashStyle.snap;
			set => SetIntNow( ShapesMaterialUtils.propDashSnap, (int)( dashStyle.snap = value ) );
		}
		public DashType DashType {
			get => dashStyle.type;
			set => SetIntNow( ShapesMaterialUtils.propDashType, (int)( dashStyle.type = value ) );
		}
		public float DashShapeModifier {
			get => dashStyle.shapeModifier;
			set => SetFloatNow( ShapesMaterialUtils.propDashShapeModifier, dashStyle.shapeModifier = value );
		}
		void SetAllDashValues( bool now ) => SetAllDashValues( dashStyle, Dashed, matchDashSpacingToSize, thickness, Geometry != LineGeometry.Volumetric3D, now );
		float GetNetDashSpacing() => GetNetDashSpacing( dashStyle, dashed, matchDashSpacingToSize, thickness );
	}

	public partial class Polygon {
		[SerializeField] private protected GradientFill fill = GradientFill.defaultFill;
		[SerializeField] private protected bool useFill;
		public GradientFill Fill {
			get => fill;
			set {
				fill = value;
				SetFillProperties();
			}
		}
		public bool UseFill {
			get => useFill;
			set {
				useFill = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
			}
		}
		public FillType FillType {
			get => fill.type;
			set {
				fill.type = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
			}
		}
		public FillSpace FillSpace {
			get => fill.space;
			set => SetIntNow( ShapesMaterialUtils.propFillSpace, (int)( fill.space = value ) );
		}
		public Vector3 FillRadialOrigin {
			get => fill.radialOrigin;
			set {
				fill.radialOrigin = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public float FillRadialRadius {
			get => fill.radialRadius;
			set {
				fill.radialRadius = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearStart {
			get => fill.linearStart;
			set {
				fill.linearStart = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearEnd {
			get => fill.linearEnd;
			set => SetVector3Now( ShapesMaterialUtils.propFillEnd, fill.linearEnd = value );
		}
		public Color FillColorStart {
			get => fill.colorStart;
			set => SetColorNow( ShapesMaterialUtils.propColor, fill.colorStart = value );
		}
		public Color FillColorEnd {
			get => fill.colorEnd;
			set => SetColorNow( ShapesMaterialUtils.propColorEnd, fill.colorEnd = value );
		}

		private void SetFillProperties() {
			if( useFill ) {
				SetInt( ShapesMaterialUtils.propFillSpace, (int)fill.space );
				SetVector4( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
				SetVector3( ShapesMaterialUtils.propFillEnd, fill.linearEnd );
				SetColor( ShapesMaterialUtils.propColor, fill.colorStart );
				SetColor( ShapesMaterialUtils.propColorEnd, fill.colorEnd );
			}

			SetInt( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
		}
	}

	public partial class Rectangle {
		[SerializeField] bool matchDashSpacingToSize = true;
		public bool MatchDashSpacingToSize {
			get => matchDashSpacingToSize;
			set {
				matchDashSpacingToSize = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] bool dashed = false;
		public bool Dashed {
			get => dashed;
			set {
				dashed = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] DashStyle dashStyle = DashStyle.defaultDashStyleRing;
		public float DashSize {
			get => dashStyle.size;
			set {
				dashStyle.size = value;
				float netDashSize = dashStyle.GetNetAbsoluteSize( dashed, thickness );
				if( matchDashSpacingToSize )
					SetFloat( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
				SetFloatNow( ShapesMaterialUtils.propDashSize, netDashSize );
			}
		}
		public float DashSpacing {
			get => matchDashSpacingToSize ? dashStyle.size : dashStyle.spacing;
			set {
				dashStyle.spacing = value;
				SetFloatNow( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
			}
		}
		public float DashOffset {
			get => dashStyle.offset;
			set => SetFloatNow( ShapesMaterialUtils.propDashOffset, dashStyle.offset = value );
		}
		public DashSpace DashSpace {
			get => dashStyle.space;
			set {
				SetInt( ShapesMaterialUtils.propDashSpace, (int)( dashStyle.space = value ) );
				SetFloatNow( ShapesMaterialUtils.propDashSize, dashStyle.GetNetAbsoluteSize( dashed, thickness ) );
			}
		}
		public DashSnapping DashSnap {
			get => dashStyle.snap;
			set => SetIntNow( ShapesMaterialUtils.propDashSnap, (int)( dashStyle.snap = value ) );
		}
		public DashType DashType {
			get => dashStyle.type;
			set => SetIntNow( ShapesMaterialUtils.propDashType, (int)( dashStyle.type = value ) );
		}
		public float DashShapeModifier {
			get => dashStyle.shapeModifier;
			set => SetFloatNow( ShapesMaterialUtils.propDashShapeModifier, dashStyle.shapeModifier = value );
		}
		void SetAllDashValues( bool now ) => SetAllDashValues( dashStyle, Dashed, matchDashSpacingToSize, thickness, setType: true, now );
		float GetNetDashSpacing() => GetNetDashSpacing( dashStyle, dashed, matchDashSpacingToSize, thickness );
		[SerializeField] private protected GradientFill fill = GradientFill.defaultFill;
		[SerializeField] private protected bool useFill;
		public GradientFill Fill {
			get => fill;
			set {
				fill = value;
				SetFillProperties();
			}
		}
		public bool UseFill {
			get => useFill;
			set {
				useFill = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
			}
		}
		public FillType FillType {
			get => fill.type;
			set {
				fill.type = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
			}
		}
		public FillSpace FillSpace {
			get => fill.space;
			set => SetIntNow( ShapesMaterialUtils.propFillSpace, (int)( fill.space = value ) );
		}
		public Vector3 FillRadialOrigin {
			get => fill.radialOrigin;
			set {
				fill.radialOrigin = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public float FillRadialRadius {
			get => fill.radialRadius;
			set {
				fill.radialRadius = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearStart {
			get => fill.linearStart;
			set {
				fill.linearStart = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearEnd {
			get => fill.linearEnd;
			set => SetVector3Now( ShapesMaterialUtils.propFillEnd, fill.linearEnd = value );
		}
		public Color FillColorStart {
			get => fill.colorStart;
			set => SetColorNow( ShapesMaterialUtils.propColor, fill.colorStart = value );
		}
		public Color FillColorEnd {
			get => fill.colorEnd;
			set => SetColorNow( ShapesMaterialUtils.propColorEnd, fill.colorEnd = value );
		}

		private void SetFillProperties() {
			if( useFill ) {
				SetInt( ShapesMaterialUtils.propFillSpace, (int)fill.space );
				SetVector4( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
				SetVector3( ShapesMaterialUtils.propFillEnd, fill.linearEnd );
				SetColor( ShapesMaterialUtils.propColor, fill.colorStart );
				SetColor( ShapesMaterialUtils.propColorEnd, fill.colorEnd );
			}

			SetInt( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
		}
	}

	public partial class RegularPolygon {
		[SerializeField] bool matchDashSpacingToSize = true;
		public bool MatchDashSpacingToSize {
			get => matchDashSpacingToSize;
			set {
				matchDashSpacingToSize = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] bool dashed = false;
		public bool Dashed {
			get => dashed;
			set {
				dashed = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] DashStyle dashStyle = DashStyle.defaultDashStyleRing;
		public float DashSize {
			get => dashStyle.size;
			set {
				dashStyle.size = value;
				float netDashSize = dashStyle.GetNetAbsoluteSize( dashed, thickness );
				if( matchDashSpacingToSize )
					SetFloat( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
				SetFloatNow( ShapesMaterialUtils.propDashSize, netDashSize );
			}
		}
		public float DashSpacing {
			get => matchDashSpacingToSize ? dashStyle.size : dashStyle.spacing;
			set {
				dashStyle.spacing = value;
				SetFloatNow( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
			}
		}
		public float DashOffset {
			get => dashStyle.offset;
			set => SetFloatNow( ShapesMaterialUtils.propDashOffset, dashStyle.offset = value );
		}
		public DashSpace DashSpace {
			get => dashStyle.space;
			set {
				SetInt( ShapesMaterialUtils.propDashSpace, (int)( dashStyle.space = value ) );
				SetFloatNow( ShapesMaterialUtils.propDashSize, dashStyle.GetNetAbsoluteSize( dashed, thickness ) );
			}
		}
		public DashSnapping DashSnap {
			get => dashStyle.snap;
			set => SetIntNow( ShapesMaterialUtils.propDashSnap, (int)( dashStyle.snap = value ) );
		}
		public DashType DashType {
			get => dashStyle.type;
			set => SetIntNow( ShapesMaterialUtils.propDashType, (int)( dashStyle.type = value ) );
		}
		public float DashShapeModifier {
			get => dashStyle.shapeModifier;
			set => SetFloatNow( ShapesMaterialUtils.propDashShapeModifier, dashStyle.shapeModifier = value );
		}
		void SetAllDashValues( bool now ) => SetAllDashValues( dashStyle, Dashed, matchDashSpacingToSize, thickness, setType: true, now );
		float GetNetDashSpacing() => GetNetDashSpacing( dashStyle, dashed, matchDashSpacingToSize, thickness );
		[SerializeField] private protected GradientFill fill = GradientFill.defaultFill;
		[SerializeField] private protected bool useFill;
		public GradientFill Fill {
			get => fill;
			set {
				fill = value;
				SetFillProperties();
			}
		}
		public bool UseFill {
			get => useFill;
			set {
				useFill = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
				SetAllMaterialProperties();
			}
		}
		public FillType FillType {
			get => fill.type;
			set {
				fill.type = value;
				SetIntNow( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
			}
		}
		public FillSpace FillSpace {
			get => fill.space;
			set => SetIntNow( ShapesMaterialUtils.propFillSpace, (int)( fill.space = value ) );
		}
		public Vector3 FillRadialOrigin {
			get => fill.radialOrigin;
			set {
				fill.radialOrigin = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public float FillRadialRadius {
			get => fill.radialRadius;
			set {
				fill.radialRadius = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearStart {
			get => fill.linearStart;
			set {
				fill.linearStart = value;
				SetVector4Now( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
			}
		}
		public Vector3 FillLinearEnd {
			get => fill.linearEnd;
			set => SetVector3Now( ShapesMaterialUtils.propFillEnd, fill.linearEnd = value );
		}
		public Color FillColorStart {
			get => fill.colorStart;
			set => SetColorNow( ShapesMaterialUtils.propColor, fill.colorStart = value );
		}
		public Color FillColorEnd {
			get => fill.colorEnd;
			set => SetColorNow( ShapesMaterialUtils.propColorEnd, fill.colorEnd = value );
		}

		private void SetFillProperties() {
			if( useFill ) {
				SetInt( ShapesMaterialUtils.propFillSpace, (int)fill.space );
				SetVector4( ShapesMaterialUtils.propFillStart, fill.GetShaderStartVector() );
				SetVector3( ShapesMaterialUtils.propFillEnd, fill.linearEnd );
				SetColor( ShapesMaterialUtils.propColor, fill.colorStart );
				SetColor( ShapesMaterialUtils.propColorEnd, fill.colorEnd );
			}

			SetInt( ShapesMaterialUtils.propFillType, fill.GetShaderFillTypeInt( useFill ) );
		}
	}

	public partial class Triangle {
		[SerializeField] bool matchDashSpacingToSize = true;
		public bool MatchDashSpacingToSize {
			get => matchDashSpacingToSize;
			set {
				matchDashSpacingToSize = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] bool dashed = false;
		public bool Dashed {
			get => dashed;
			set {
				dashed = value;
				SetAllDashValues( now: true );
			}
		}
		[SerializeField] DashStyle dashStyle = DashStyle.defaultDashStyleRing;
		public float DashSize {
			get => dashStyle.size;
			set {
				dashStyle.size = value;
				float netDashSize = dashStyle.GetNetAbsoluteSize( dashed, thickness );
				if( matchDashSpacingToSize )
					SetFloat( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
				SetFloatNow( ShapesMaterialUtils.propDashSize, netDashSize );
			}
		}
		public float DashSpacing {
			get => matchDashSpacingToSize ? dashStyle.size : dashStyle.spacing;
			set {
				dashStyle.spacing = value;
				SetFloatNow( ShapesMaterialUtils.propDashSpacing, GetNetDashSpacing() );
			}
		}
		public float DashOffset {
			get => dashStyle.offset;
			set => SetFloatNow( ShapesMaterialUtils.propDashOffset, dashStyle.offset = value );
		}
		public DashSpace DashSpace {
			get => dashStyle.space;
			set {
				SetInt( ShapesMaterialUtils.propDashSpace, (int)( dashStyle.space = value ) );
				SetFloatNow( ShapesMaterialUtils.propDashSize, dashStyle.GetNetAbsoluteSize( dashed, thickness ) );
			}
		}
		public DashSnapping DashSnap {
			get => dashStyle.snap;
			set => SetIntNow( ShapesMaterialUtils.propDashSnap, (int)( dashStyle.snap = value ) );
		}
		public DashType DashType {
			get => dashStyle.type;
			set => SetIntNow( ShapesMaterialUtils.propDashType, (int)( dashStyle.type = value ) );
		}
		public float DashShapeModifier {
			get => dashStyle.shapeModifier;
			set => SetFloatNow( ShapesMaterialUtils.propDashShapeModifier, dashStyle.shapeModifier = value );
		}
		void SetAllDashValues( bool now ) => SetAllDashValues( dashStyle, Dashed, matchDashSpacingToSize, thickness, setType: true, now );
		float GetNetDashSpacing() => GetNetDashSpacing( dashStyle, dashed, matchDashSpacingToSize, thickness );
	}

}
