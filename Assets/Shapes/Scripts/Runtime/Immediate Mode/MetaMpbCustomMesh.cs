using System.Collections.Generic;
using UnityEngine;

// Shapes © <PERSON><PERSON> - https://twitter.com/FreyaHolmer/
// Website & Documentation - https://acegikmo.com/shapes/
namespace Shapes {

	internal class MpbCustomMesh : MetaMpb {

		protected override void TransferShapeProperties() {
			// Transfer( ShapesMaterialUtils.propRect, rect );
			// Transfer( ShapesMaterialUtils.propUvs, uvs );
			// Transfer( ShapesMaterialUtils.propMainTex, ref texture );
		}

	}

}