{"name": "com.acegikmo.shapes", "displayName": "<PERSON><PERSON><PERSON>", "version": "4.5.1", "unity": "2022.3", "description": "A real-time vector graphics library for Unity to draw Lines, Polylines, Discs, Arcs, Rectangles, Spheres, Tori, and other primitives both as easy-to-use components, and with an immediate-mode API for code-based procedural drawing. Shapes supports drawing both at runtime as well as for debugging purposes in OnDrawGizmos!\n\n• acegikmo.com/shapes\n• <EMAIL>\n• twitter.com/FreyaHolmer\n• discord.gg/aV62hk3\n• patreon.com/acegikmo\n\nMade possible by my wonderful supporters on Patreon!! ❤", "documentationUrl": "https://acegikmo.com/shapes/", "dependencies": {"com.unity.textmeshpro": "3.0.9"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://acegikmo.com/"}, "type": "library"}